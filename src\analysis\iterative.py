# Iterative analysis logic
import time
from typing import List, Dict, Any, Optional, Callable, Set, Tuple
from datetime import datetime
from collections import deque
import math

from ..models.topic import Topic, TopicNode
from ..models.result import AnalysisResult, AnalysisStatus, AnalysisType
from ..models.hierarchy import Hierarchy
from ..ollama.api import get_ollama_client, OllamaAPIError
from ..ollama.model_manager import get_model_manager
from ..prompting.prompt_editor import PromptEditor
from ..utils.logging_config import AnalysisLogger
from ..utils.eta import get_eta_estimator
from ..config.settings import settings


class IterativeAnalyzer:
    """Performs iterative breadth-first analysis with refinement capabilities."""

    def __init__(self,
                 model_name: Optional[str] = None,
                 max_iterations: int = 5,
                 convergence_threshold: float = 0.95,
                 batch_size: int = 10):
        self.ollama_client = get_ollama_client()
        self.model_manager = get_model_manager()
        self.model_name = model_name or settings.get("ollama", "default_model", "llama2")
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold
        self.batch_size = batch_size

        self.prompt_editor = PromptEditor()
        self.logger = AnalysisLogger("iterative")

        # Analysis state
        self.results: Dict[str, AnalysisResult] = {}
        self.iteration_history: List[Dict[str, Any]] = []
        self.cancelled = False

        # Progress tracking
        self.progress_callback: Optional[Callable] = None
        self.total_nodes = 0
        self.processed_nodes = 0
        self.current_iteration = 0
        self.eta_estimator = get_eta_estimator()
        self.current_task_id: Optional[str] = None

    def set_progress_callback(self, callback: Callable[[int, int, str], None]):
        """Set callback for progress updates."""
        self.progress_callback = callback

    def cancel_analysis(self):
        """Cancel the current analysis."""
        self.cancelled = True
        self.logger.logger.info("Iterative analysis cancellation requested")

    def analyze_hierarchy(self,
                         hierarchy: Hierarchy,
                         system_prompt: Optional[str] = None,
                         custom_prompts: Optional[Dict[int, str]] = None,
                         analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, AnalysisResult]:
        """Analyze hierarchy using iterative breadth-first approach."""

        self.logger.log_start("hierarchy", "iterative")
        start_time = time.time()

        # Reset state
        self.results.clear()
        self.iteration_history.clear()
        self.cancelled = False
        self.processed_nodes = 0
        self.current_iteration = 0

        # Count total nodes
        self.total_nodes = sum(topic.get_node_count() for topic in hierarchy.topics.values())

        # Start ETA tracking - estimate total work as nodes * iterations
        estimated_total_work = self.total_nodes * self.max_iterations
        self.current_task_id = f"iterative_analysis_{int(time.time())}"
        self.eta_estimator.start_task(
            self.current_task_id,
            "iterative",
            estimated_total_work,
            complexity={
                'hierarchy_size': len(hierarchy.topics),
                'max_iterations': self.max_iterations,
                'batch_size': self.batch_size
            }
        )

        try:
            # Validate model
            model_validation = self.model_manager.validate_model(self.model_name)
            if not model_validation['valid']:
                raise ValueError(f"Model validation failed: {model_validation['errors']}")

            # Collect all nodes by level across all topics
            nodes_by_level = self._collect_nodes_by_level(hierarchy)

            # Perform iterative analysis
            for iteration in range(self.max_iterations):
                if self.cancelled:
                    break

                self.current_iteration = iteration + 1
                self.logger.logger.info(f"Starting iteration {self.current_iteration}")

                iteration_results = self._perform_iteration(
                    hierarchy,
                    nodes_by_level,
                    system_prompt=system_prompt,
                    custom_prompts=custom_prompts,
                    analysis_options=analysis_options
                )

                # Check for convergence
                if self._check_convergence(iteration_results):
                    self.logger.logger.info(f"Convergence achieved at iteration {self.current_iteration}")
                    break

            duration = time.time() - start_time
            self.logger.log_completion("hierarchy", "iterative", duration)

            # Send final progress update to show 100% completion
            if self.progress_callback:
                self.progress_callback(
                    self.total_nodes * self.max_iterations,  # Show as complete
                    self.total_nodes * self.max_iterations,
                    "Analysis completed successfully"
                )

            # Complete ETA tracking
            if self.current_task_id:
                self.eta_estimator.complete_task(self.current_task_id)

            return self.results

        except Exception as e:
            self.logger.log_error(e, "iterative hierarchy analysis")
            raise

    def analyze_topic(self,
                     topic: Topic,
                     system_prompt: Optional[str] = None,
                     custom_prompts: Optional[Dict[int, str]] = None,
                     analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, AnalysisResult]:
        """Analyze a single topic iteratively."""

        # Create temporary hierarchy with single topic
        temp_hierarchy = Hierarchy("temp", "Temporary hierarchy for single topic")
        temp_hierarchy.add_topic(topic)

        return self.analyze_hierarchy(
            temp_hierarchy,
            system_prompt=system_prompt,
            custom_prompts=custom_prompts,
            analysis_options=analysis_options
        )

    def _collect_nodes_by_level(self, hierarchy: Hierarchy) -> Dict[int, List[Tuple[str, TopicNode]]]:
        """Collect all nodes organized by their hierarchy level."""
        nodes_by_level = {}

        for topic_id, topic in hierarchy.topics.items():
            for node_id, node in topic.nodes.items():
                level = node.level
                if level not in nodes_by_level:
                    nodes_by_level[level] = []
                nodes_by_level[level].append((topic_id, node))

        return nodes_by_level

    def _perform_iteration(self,
                          hierarchy: Hierarchy,
                          nodes_by_level: Dict[int, List[Tuple[str, TopicNode]]],
                          system_prompt: Optional[str] = None,
                          custom_prompts: Optional[Dict[int, str]] = None,
                          analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Perform a single iteration of analysis."""

        iteration_results = {
            'iteration': self.current_iteration,
            'processed_nodes': 0,
            'improved_nodes': 0,
            'quality_scores': [],
            'start_time': datetime.now()
        }

        # Process nodes level by level (breadth-first)
        for level in sorted(nodes_by_level.keys()):
            if self.cancelled:
                break

            level_nodes = nodes_by_level[level]
            self.logger.logger.info(f"Processing level {level} with {len(level_nodes)} nodes")

            # Process nodes in batches
            for batch_start in range(0, len(level_nodes), self.batch_size):
                if self.cancelled:
                    break

                batch_end = min(batch_start + self.batch_size, len(level_nodes))
                batch_nodes = level_nodes[batch_start:batch_end]

                batch_results = self._process_node_batch(
                    hierarchy,
                    batch_nodes,
                    system_prompt=system_prompt,
                    custom_prompts=custom_prompts,
                    analysis_options=analysis_options
                )

                # Update iteration results
                iteration_results['processed_nodes'] += len(batch_results)

                for node_id, result in batch_results.items():
                    if result.status == AnalysisStatus.COMPLETED:
                        quality_score = result.calculate_quality_score()
                        iteration_results['quality_scores'].append(quality_score)

                        # Check if this is an improvement
                        if self._is_improvement(node_id, result):
                            iteration_results['improved_nodes'] += 1

                        self.results[node_id] = result

        iteration_results['end_time'] = datetime.now()
        iteration_results['duration'] = (iteration_results['end_time'] - iteration_results['start_time']).total_seconds()
        iteration_results['avg_quality'] = (
            sum(iteration_results['quality_scores']) / len(iteration_results['quality_scores'])
            if iteration_results['quality_scores'] else 0.0
        )

        self.iteration_history.append(iteration_results)
        return iteration_results

    def _process_node_batch(self,
                           hierarchy: Hierarchy,
                           batch_nodes: List[Tuple[str, TopicNode]],
                           system_prompt: Optional[str] = None,
                           custom_prompts: Optional[Dict[int, str]] = None,
                           analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, AnalysisResult]:
        """Process a batch of nodes."""

        batch_results = {}

        for topic_id, node in batch_nodes:
            if self.cancelled:
                break

            topic = hierarchy.get_topic(topic_id)
            if not topic:
                continue

            # Track timing for this node
            node_start_time = time.time()

            # Create or update analysis result
            result = self._analyze_node_iterative(
                topic,
                node,
                system_prompt=system_prompt,
                custom_prompts=custom_prompts,
                analysis_options=analysis_options
            )

            # Calculate response time for this node
            node_response_time = time.time() - node_start_time

            batch_results[node.id] = result

            # Update progress
            self.processed_nodes += 1

            # Update ETA estimator
            if self.current_task_id:
                eta_info = self.eta_estimator.update_task_progress(
                    self.current_task_id,
                    self.processed_nodes,
                    node_response_time
                )

                # Enhanced progress callback with ETA
                if self.progress_callback:
                    progress_msg = f"Iteration {self.current_iteration}, Level {node.level}: {node.content[:50]}... | ETA: {eta_info['eta_formatted']} | {eta_info['items_per_second']:.1f} nodes/sec"
                    self.progress_callback(
                        self.processed_nodes,
                        self.total_nodes * self.max_iterations,  # Approximate total work
                        progress_msg
                    )
            elif self.progress_callback:
                # Fallback without ETA
                progress_msg = f"Iteration {self.current_iteration}, Level {node.level}: {node.content[:50]}..."
                self.progress_callback(
                    self.processed_nodes,
                    self.total_nodes * self.max_iterations,  # Approximate total work
                    progress_msg
                )

        return batch_results

    def _analyze_node_iterative(self,
                               topic: Topic,
                               node: TopicNode,
                               system_prompt: Optional[str] = None,
                               custom_prompts: Optional[Dict[int, str]] = None,
                               analysis_options: Optional[Dict[str, Any]] = None) -> AnalysisResult:
        """Analyze a single node with iterative refinement."""

        # Get existing result or create new one
        if node.id in self.results:
            result = self.results[node.id]
            # Update for refinement
            result.metadata.parameters['iteration'] = self.current_iteration
        else:
            result = AnalysisResult(topic_id=topic.id, node_id=node.id)
            result.metadata.analysis_type = AnalysisType.ITERATIVE
            result.metadata.model_name = self.model_name

        result.set_status(AnalysisStatus.RUNNING)

        try:
            # Build context including previous iterations
            context = self._build_iterative_context(topic, node)

            # Generate prompt with refinement instructions
            prompt = self._generate_iterative_prompt(
                node,
                context,
                custom_prompts,
                analysis_options
            )

            result.metadata.prompt_template = prompt

            # Perform analysis
            start_time = time.time()

            analysis_content = self._perform_iterative_analysis(
                prompt,
                system_prompt,
                analysis_options,
                previous_content=result.content if result.content else None
            )

            response_time = time.time() - start_time

            # Update result
            result.set_content(analysis_content)
            result.set_status(AnalysisStatus.COMPLETED)

            # Calculate quality metrics
            self._calculate_quality_metrics(result, node)

            # Update performance metrics
            self.model_manager.update_model_performance(
                self.model_name,
                response_time,
                len(analysis_content.split()),
                True
            )

        except Exception as e:
            result.set_status(AnalysisStatus.FAILED)
            result.add_error(str(e))
            self.logger.log_error(e, f"iterative node {node.id}")

            # Update performance metrics for failure
            self.model_manager.update_model_performance(
                self.model_name, 0, 0, False
            )

        return result

    def _build_iterative_context(self, topic: Topic, node: TopicNode) -> str:
        """Build context for iterative analysis including previous results."""

        # Get path to root
        path_nodes = topic.get_path_to_root(node.id)
        context_parts = []

        # Add hierarchical context
        for path_node in path_nodes:
            context_parts.append(f"Level {path_node.level}: {path_node.content}")

            # Add previous analysis if available
            if path_node.id in self.results:
                prev_result = self.results[path_node.id]
                if prev_result.content and prev_result.status == AnalysisStatus.COMPLETED:
                    context_parts.append(f"Previous analysis: {prev_result.content[:200]}...")

        # Add sibling context
        if node.parent_id:
            parent = topic.get_node(node.parent_id)
            if parent:
                siblings = topic.get_children(parent.id)
                sibling_analyses = []

                for sibling in siblings:
                    if sibling.id != node.id and sibling.id in self.results:
                        sibling_result = self.results[sibling.id]
                        if sibling_result.content:
                            sibling_analyses.append(f"{sibling.content}: {sibling_result.content[:100]}...")

                if sibling_analyses:
                    context_parts.append("Related analyses: " + "; ".join(sibling_analyses))

        return "\n\n".join(context_parts)

    def _generate_iterative_prompt(self,
                                  node: TopicNode,
                                  context: str,
                                  custom_prompts: Optional[Dict[int, str]] = None,
                                  analysis_options: Optional[Dict[str, Any]] = None) -> str:
        """Generate prompt for iterative analysis."""

        # Base prompt
        if custom_prompts and node.level in custom_prompts:
            base_prompt = custom_prompts[node.level]
        else:
            base_prompt = self._get_iterative_prompt_for_level(node.level)

        # Build full prompt
        full_prompt = f"""ITERATIVE ANALYSIS - Iteration {self.current_iteration}

Context:
{context}

Current Focus: {node.content}
Level: {node.level}

{base_prompt}

REFINEMENT INSTRUCTIONS:
- Build upon previous analyses in the context
- Identify gaps or areas for improvement
- Provide more detailed or nuanced insights
- Ensure consistency with related analyses
- Focus on quality and depth over quantity"""

        # Add specific refinement instructions based on iteration
        if self.current_iteration > 1:
            full_prompt += f"""

ITERATION {self.current_iteration} FOCUS:
- Refine and improve upon previous iteration
- Address any inconsistencies or gaps
- Provide deeper insights and analysis
- Ensure coherence with the broader context"""

        return full_prompt

    def _get_iterative_prompt_for_level(self, level: int) -> str:
        """Get iterative prompt template for a specific level."""
        iterative_prompts = {
            0: "Provide a comprehensive and refined overview of this main topic. Build upon any previous analysis to create a more complete understanding.",
            1: "Analyze this category with increased depth and nuance. Refine your understanding based on the broader context and related analyses.",
            2: "Examine this subcategory with enhanced detail. Integrate insights from related areas and provide refined analysis.",
            3: "Provide detailed and refined analysis of this specific aspect. Build upon previous insights and add new perspectives.",
            4: "Give an expert-level, refined examination of this detailed element. Synthesize all available context for the most comprehensive analysis."
        }

        return iterative_prompts.get(level, "Provide refined and comprehensive analysis of this topic.")

    def _perform_iterative_analysis(self,
                                   prompt: str,
                                   system_prompt: Optional[str] = None,
                                   analysis_options: Optional[Dict[str, Any]] = None,
                                   previous_content: Optional[str] = None) -> str:
        """Perform iterative analysis with refinement."""

        # Prepare generation options
        generation_options = {
            'temperature': 0.6,  # Slightly lower for more focused refinement
            'top_p': 0.9,
            'num_predict': 600  # Slightly longer for refinement
        }

        if analysis_options and 'generation_options' in analysis_options:
            generation_options.update(analysis_options['generation_options'])

        # Add previous content to system prompt if available
        if previous_content and system_prompt:
            enhanced_system_prompt = f"{system_prompt}\n\nPrevious analysis to build upon:\n{previous_content}"
        elif previous_content:
            enhanced_system_prompt = f"Build upon and refine this previous analysis:\n{previous_content}"
        else:
            enhanced_system_prompt = system_prompt

        try:
            response = self.ollama_client.generate_simple(
                model=self.model_name,
                prompt=prompt,
                system=enhanced_system_prompt,
                options=generation_options
            )

            return response

        except OllamaAPIError as e:
            raise Exception(f"Ollama API error: {str(e)}")
        except Exception as e:
            raise Exception(f"Iterative analysis generation failed: {str(e)}")

    def _calculate_quality_metrics(self, result: AnalysisResult, node: TopicNode):
        """Calculate quality metrics for the analysis result."""

        content = result.content
        if not content:
            return

        # Basic metrics
        word_count = len(content.split())
        char_count = len(content)

        # Completeness score (based on content length and structure)
        completeness = min(1.0, word_count / 200)  # Assume 200 words is complete

        # Relevance score (simple keyword matching)
        node_keywords = set(node.content.lower().split())
        content_words = set(content.lower().split())
        relevance = len(node_keywords.intersection(content_words)) / len(node_keywords) if node_keywords else 0.0

        # Clarity score (based on sentence structure - simplified)
        sentences = content.split('.')
        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences) if sentences else 0
        clarity = max(0.0, min(1.0, 1.0 - abs(avg_sentence_length - 15) / 15))  # Optimal ~15 words per sentence

        # Store quality metrics
        result.quality_metrics = {
            'completeness': completeness,
            'relevance': relevance,
            'clarity': clarity,
            'word_count': word_count,
            'char_count': char_count
        }

    def _is_improvement(self, node_id: str, new_result: AnalysisResult) -> bool:
        """Check if the new result is an improvement over the previous one."""

        if node_id not in self.results:
            return True  # First analysis is always an improvement

        old_result = self.results[node_id]

        # Compare quality scores
        old_quality = old_result.calculate_quality_score()
        new_quality = new_result.calculate_quality_score()

        return new_quality > old_quality

    def _check_convergence(self, iteration_results: Dict[str, Any]) -> bool:
        """Check if the analysis has converged."""

        if len(self.iteration_history) < 2:
            return False

        current_quality = iteration_results['avg_quality']
        previous_quality = self.iteration_history[-2]['avg_quality']

        # Check if improvement is below threshold
        improvement = abs(current_quality - previous_quality)
        improvement_rate = improvement / previous_quality if previous_quality > 0 else 0

        # Check if quality is above convergence threshold
        quality_converged = current_quality >= self.convergence_threshold

        # Check if improvement rate is small
        improvement_converged = improvement_rate < 0.05  # Less than 5% improvement

        return quality_converged or improvement_converged

    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get summary of the iterative analysis."""

        if not self.results:
            return {'status': 'no_results'}

        total_results = len(self.results)
        completed_results = sum(1 for r in self.results.values() if r.status == AnalysisStatus.COMPLETED)
        failed_results = sum(1 for r in self.results.values() if r.status == AnalysisStatus.FAILED)

        # Calculate metrics across iterations
        quality_progression = [iter_data['avg_quality'] for iter_data in self.iteration_history]

        return {
            'status': 'completed' if not self.cancelled else 'cancelled',
            'total_results': total_results,
            'completed_results': completed_results,
            'failed_results': failed_results,
            'success_rate': completed_results / total_results if total_results > 0 else 0.0,
            'iterations_performed': len(self.iteration_history),
            'quality_progression': quality_progression,
            'final_quality': quality_progression[-1] if quality_progression else 0.0,
            'converged': self._check_convergence(self.iteration_history[-1]) if self.iteration_history else False,
            'model_used': self.model_name,
            'analysis_type': 'iterative'
        }


# Convenience functions
def analyze_hierarchy_iterative(hierarchy: Hierarchy,
                               model_name: Optional[str] = None,
                               **kwargs) -> Dict[str, AnalysisResult]:
    """Convenience function to analyze hierarchy iteratively."""
    analyzer = IterativeAnalyzer(model_name=model_name)
    return analyzer.analyze_hierarchy(hierarchy, **kwargs)

def analyze_topic_iterative(topic: Topic,
                           model_name: Optional[str] = None,
                           **kwargs) -> Dict[str, AnalysisResult]:
    """Convenience function to analyze topic iteratively."""
    analyzer = IterativeAnalyzer(model_name=model_name)
    return analyzer.analyze_topic(topic, **kwargs)
