# Tests for GUI module
import unittest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.gui.prompt_storage_service import PromptStorageService, UserPrompt, DataMigrationUtility


class TestUserPrompt(unittest.TestCase):
    """Test cases for UserPrompt class."""

    def test_user_prompt_creation(self):
        """Test creating a UserPrompt."""
        prompt = UserPrompt(
            id="test_1",
            name="Test Prompt",
            content="This is a test prompt",
            category="Test"
        )

        self.assertEqual(prompt.id, "test_1")
        self.assertEqual(prompt.name, "Test Prompt")
        self.assertEqual(prompt.content, "This is a test prompt")
        self.assertEqual(prompt.category, "Test")
        self.assertEqual(prompt.usage_count, 0)
        self.assertFalse(prompt.is_favorite)
        self.assertIsInstance(prompt.created_at, datetime)

    def test_user_prompt_to_dict(self):
        """Test converting UserPrompt to dictionary."""
        prompt = UserPrompt(
            id="test_1",
            name="Test Prompt",
            content="This is a test prompt"
        )

        data = prompt.to_dict()

        self.assertIsInstance(data, dict)
        self.assertEqual(data['id'], "test_1")
        self.assertEqual(data['name'], "Test Prompt")
        self.assertEqual(data['content'], "This is a test prompt")
        self.assertIn('created_at', data)
        self.assertIn('updated_at', data)

    def test_user_prompt_from_dict(self):
        """Test creating UserPrompt from dictionary."""
        data = {
            'id': 'test_1',
            'name': 'Test Prompt',
            'content': 'This is a test prompt',
            'category': 'Test',
            'tags': ['test', 'example'],
            'usage_count': 5,
            'is_favorite': True,
            'created_at': '2023-01-01T12:00:00',
            'updated_at': '2023-01-02T12:00:00'
        }

        prompt = UserPrompt.from_dict(data)

        self.assertEqual(prompt.id, 'test_1')
        self.assertEqual(prompt.name, 'Test Prompt')
        self.assertEqual(prompt.content, 'This is a test prompt')
        self.assertEqual(prompt.category, 'Test')
        self.assertEqual(prompt.tags, ['test', 'example'])
        self.assertEqual(prompt.usage_count, 5)
        self.assertTrue(prompt.is_favorite)


class TestPromptStorageService(unittest.TestCase):
    """Test cases for PromptStorageService class."""

    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        self.storage_dir = Path(self.temp_dir) / "prompts"
        self.storage_dir.mkdir(parents=True, exist_ok=True)

        # Create service with custom storage directory
        self.service = PromptStorageService()
        self.service.storage_dir = self.storage_dir
        self.service.user_prompts_file = self.storage_dir / "user_prompts.json"
        self.service.recent_prompts_file = self.storage_dir / "recent_prompts.json"
        self.service.user_prompts = {}
        self.service.recent_prompts = []

    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)

    def test_save_prompt(self):
        """Test saving a prompt."""
        prompt = self.service.save_prompt("Test content", "Test Prompt", "Test Category")

        self.assertIsInstance(prompt, UserPrompt)
        self.assertEqual(prompt.name, "Test Prompt")
        self.assertEqual(prompt.content, "Test content")
        self.assertEqual(prompt.category, "Test Category")
        self.assertIn(prompt.id, self.service.user_prompts)

    def test_get_prompt(self):
        """Test getting a prompt by ID."""
        saved_prompt = self.service.save_prompt("Test content", "Test Prompt")
        retrieved_prompt = self.service.get_prompt(saved_prompt.id)

        self.assertIsNotNone(retrieved_prompt)
        self.assertEqual(retrieved_prompt.id, saved_prompt.id)
        self.assertEqual(retrieved_prompt.name, saved_prompt.name)

    def test_get_all_prompts(self):
        """Test getting all prompts."""
        self.service.save_prompt("Content 1", "Prompt 1")
        self.service.save_prompt("Content 2", "Prompt 2")

        all_prompts = self.service.get_all_prompts()

        self.assertEqual(len(all_prompts), 2)
        self.assertIsInstance(all_prompts[0], UserPrompt)

    def test_update_prompt(self):
        """Test updating a prompt."""
        prompt = self.service.save_prompt("Original content", "Original Name")

        success = self.service.update_prompt(prompt.id, name="Updated Name", is_favorite=True)

        self.assertTrue(success)
        updated_prompt = self.service.get_prompt(prompt.id)
        self.assertEqual(updated_prompt.name, "Updated Name")
        self.assertTrue(updated_prompt.is_favorite)

    def test_delete_prompt(self):
        """Test deleting a prompt."""
        prompt = self.service.save_prompt("Test content", "Test Prompt")

        success = self.service.delete_prompt(prompt.id)

        self.assertTrue(success)
        self.assertIsNone(self.service.get_prompt(prompt.id))

    def test_use_prompt(self):
        """Test using a prompt (updating usage statistics)."""
        prompt = self.service.save_prompt("Test content", "Test Prompt")
        original_usage = prompt.usage_count

        content = self.service.use_prompt(prompt.id)

        self.assertEqual(content, "Test content")
        self.assertEqual(prompt.usage_count, original_usage + 1)
        self.assertIsNotNone(prompt.last_used)
        self.assertIn(prompt.id, self.service.recent_prompts)

    def test_search_prompts(self):
        """Test searching prompts."""
        self.service.save_prompt("Machine learning content", "ML Prompt")
        self.service.save_prompt("Data analysis content", "Analysis Prompt")
        self.service.save_prompt("Web development content", "Web Prompt")

        results = self.service.search_prompts("machine")

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].name, "ML Prompt")

    def test_get_categories(self):
        """Test getting unique categories."""
        self.service.save_prompt("Content 1", "Prompt 1", "Category A")
        self.service.save_prompt("Content 2", "Prompt 2", "Category B")
        self.service.save_prompt("Content 3", "Prompt 3", "Category A")

        categories = self.service.get_categories()

        self.assertEqual(len(categories), 2)
        self.assertIn("Category A", categories)
        self.assertIn("Category B", categories)

    def test_get_usage_analytics(self):
        """Test getting usage analytics."""
        prompt1 = self.service.save_prompt("Content 1", "Prompt 1", "Category A")
        prompt2 = self.service.save_prompt("Content 2", "Prompt 2", "Category B")

        # Use prompts to generate usage data
        self.service.use_prompt(prompt1.id)
        self.service.use_prompt(prompt1.id)
        self.service.use_prompt(prompt2.id)

        analytics = self.service.get_usage_analytics()

        self.assertEqual(analytics['total_prompts'], 2)
        self.assertEqual(analytics['total_usage'], 3)
        self.assertEqual(len(analytics['most_used']), 2)
        self.assertEqual(len(analytics['categories']), 2)


if __name__ == '__main__':
    unittest.main()
