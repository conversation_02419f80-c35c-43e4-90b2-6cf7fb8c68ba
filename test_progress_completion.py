#!/usr/bin/env python3
"""
Test script to verify progress dialog completion fixes.
"""

import sys
import os
import time
import threading
import tkinter as tk
from tkinter import ttk

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.gui.progress_dialog import ProgressDialog
    from src.utils.logging_config import setup_logging
    
    # Setup logging
    setup_logging()
    
    def test_progress_completion():
        """Test progress dialog completion behavior."""
        print("Testing progress dialog completion...")
        
        root = tk.Tk()
        root.title("Progress Completion Test")
        root.geometry("400x200")
        
        def run_test():
            """Run the progress test."""
            dialog = ProgressDialog(root, "Test Progress", can_cancel=True)
            
            def simulate_work():
                """Simulate work with progress updates."""
                try:
                    for i in range(1, 11):
                        if dialog.is_cancelled():
                            break
                        
                        # Update progress
                        dialog.update_progress(i * 10, 100, f"Processing step {i}/10")
                        dialog.add_detail(f"Completed step {i}")
                        time.sleep(0.2)  # Simulate work
                    
                    if not dialog.is_cancelled():
                        # Test completion
                        dialog.set_complete(True, "Test operation completed successfully!")
                        print("✓ Progress dialog marked as complete")
                        
                        # Verify that progress shows 100%
                        if dialog.current_value == dialog.maximum_value:
                            print("✓ Progress value correctly set to maximum")
                        else:
                            print(f"✗ Progress value incorrect: {dialog.current_value}/{dialog.maximum_value}")
                    else:
                        dialog.set_complete(False, "Test operation cancelled")
                        print("✓ Progress dialog marked as cancelled")
                        
                except Exception as e:
                    print(f"✗ Error during test: {e}")
                    dialog.set_complete(False, f"Test failed: {str(e)}")
            
            # Run work in background thread
            thread = threading.Thread(target=simulate_work)
            thread.daemon = True
            thread.start()
            
            # Show dialog
            dialog.show()
        
        # Create test button
        test_button = ttk.Button(root, text="Start Progress Test", command=run_test)
        test_button.pack(pady=50)
        
        # Add instructions
        instructions = tk.Label(root, text="Click 'Start Progress Test' to test progress completion.\nWatch for 100% progress and 'ETA: Complete' at the end.", 
                               wraplength=350, justify=tk.CENTER)
        instructions.pack(pady=20)
        
        print("Progress completion test ready. Click the button to start.")
        root.mainloop()
    
    if __name__ == "__main__":
        test_progress_completion()
        
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project root directory.")
    sys.exit(1)
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
