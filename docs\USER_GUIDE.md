# Deep Research Tool - User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Creating Hierarchies](#creating-hierarchies)
3. [Running Analysis](#running-analysis)
4. [Understanding Results](#understanding-results)
5. [Prompt Manager](#prompt-manager)
6. [Advanced Features](#advanced-features)
7. [Troubleshooting](#troubleshooting)

## Getting Started

### First Launch
After installation, start with a system check:
```bash
python -m src.main --check-system
```

This will verify:
- Python version compatibility
- Ollama connection and available models
- Required dependencies
- Directory permissions

### Choosing an Interface

#### CLI Mode (Recommended for Beginners)
```bash
python -m src.main
```
- Interactive menu system
- Step-by-step guidance
- Built-in help and validation

#### GUI Mode (Visual Interface)
```bash
python -m src.main --gui
```
- Point-and-click interface
- Mind map visualization
- Real-time progress tracking

#### Designer Mode (Hierarchy Creation)
```bash
python -m src.main --designer
```
- Specialized tool for creating hierarchies
- Advanced editing features
- Export capabilities

## Creating Hierarchies

### Understanding Hierarchy Structure
The Deep Research Tool supports up to 5 levels of hierarchy:
```
Level 1: Main Topic (e.g., "Artificial Intelligence")
├── Level 2: Category (e.g., "Machine Learning")
    ├── Level 3: Subcategory (e.g., "Deep Learning")
        ├── Level 4: Aspect (e.g., "Neural Networks")
            ├── Level 5: Detail (e.g., "Convolutional Networks")
```

### Method 1: Using the Hierarchy Designer

1. **Launch Designer**:
   ```bash
   python -m src.main --designer
   ```

2. **Create New Hierarchy**:
   - Select "Create New Hierarchy"
   - Enter hierarchy name and description
   - Configure level names (optional)

3. **Add Topics**:
   - Use "Add Topics" menu
   - Enter content for each level
   - Leave levels empty to finish a branch

4. **Validate and Export**:
   - Use "Validate Hierarchy" to check structure
   - Export to CSV or JSON for reuse

### Method 2: CSV Import

Create a CSV file with up to 5 columns:
```csv
Topic,Category,Subcategory,Aspect,Detail
AI,Machine Learning,Deep Learning,Neural Networks,CNNs
AI,Machine Learning,Deep Learning,Neural Networks,RNNs
AI,Machine Learning,Supervised Learning,Classification,SVM
AI,Natural Language,Processing,Sentiment Analysis,
Data Science,Statistics,Descriptive,Measures,
Data Science,Visualization,Charts,Bar Charts,
```

**CSV Guidelines**:
- First row can be headers (will be auto-detected)
- Empty cells end the hierarchy branch
- Consistent naming improves analysis quality
- Use descriptive, specific terms

### Method 3: JSON Import

For complex hierarchies, use JSON format:
```json
{
  "name": "Research Hierarchy",
  "description": "My research topics",
  "topics": [
    {
      "name": "AI Research",
      "layers": ["AI", "Machine Learning", "Deep Learning"]
    }
  ]
}
```

## Running Analysis

### Analysis Types

#### Recursive Analysis (Depth-First)
- **Best for**: Detailed, contextual analysis
- **How it works**: Analyzes parent topics first, passes context to children
- **Use when**: You want deep, interconnected insights

#### Iterative Analysis (Breadth-First)
- **Best for**: Comprehensive, refined analysis
- **How it works**: Multiple passes with refinement and convergence detection
- **Use when**: You want thorough, polished results

### Starting Analysis

#### CLI Mode
1. Load or create hierarchy
2. Select "Run Analysis" from main menu
3. Choose analysis type (recursive/iterative)
4. Enter system prompt (or use default)
5. Monitor progress and wait for completion

#### GUI Mode
1. Load hierarchy using File menu
2. Select analysis type from Analysis menu
3. Configure options in dialog
4. Monitor progress in status bar
5. View results in results panel

#### Batch Mode
```bash
python -m src.main --batch input.csv \
  --analysis-type recursive \
  --model llama2 \
  --output results.json \
  --system-prompt "Provide detailed technical analysis"
```

### System Prompts

The system prompt guides the AI's analysis approach:

**Default**: "Provide a comprehensive analysis of the given topic."

**Technical Focus**: "Analyze from a technical perspective, focusing on implementation details, challenges, and best practices."

**Business Focus**: "Analyze from a business perspective, considering market impact, opportunities, and strategic implications."

**Research Focus**: "Provide an academic analysis with current research trends, key papers, and future directions."

## Understanding Results

### Result Components

Each analysis result contains:
- **Content**: The AI-generated analysis
- **Metadata**: Model used, timing, token counts
- **Quality Metrics**: Automated quality assessment
- **Context**: Parent topic context used

### Quality Metrics

Results are automatically scored on:
- **Completeness** (0-1): How thoroughly the topic is covered
- **Relevance** (0-1): How well the analysis matches the topic
- **Accuracy** (0-1): Factual correctness and consistency
- **Clarity** (0-1): Readability and organization

**Overall Quality Score**: Weighted average of all metrics

### Viewing Results

#### CLI Mode
- Use "View Results" menu
- Browse by topic or quality score
- Search within results
- Export in multiple formats

#### GUI Mode
- Results appear in results panel
- Click nodes to view details
- Use tabs for content/metadata/quality
- Mind map shows analysis status with colors

### Exporting Results

**JSON Format** (Complete data):
```json
{
  "export_time": "2024-01-15T10:30:00",
  "hierarchy": {...},
  "results": {...},
  "summary": {...}
}
```

**CSV Format** (Tabular data):
```csv
Node ID,Topic,Content,Quality Score,Status
node_123,AI > ML,Analysis content...,0.85,completed
```

**Text Format** (Human-readable):
```
Analysis Results Export
Generated: 2024-01-15T10:30:00
========================

Topic: AI > Machine Learning
Quality: 0.85
Content: Analysis content...
```

## Prompt Manager

The Deep Research Tool includes a comprehensive Prompt Manager that allows you to create, save, organize, and reuse prompts for your analysis workflows.

### Enhanced Prompt Input Dialog

When running analysis, you'll see an enhanced prompt dialog instead of a simple text box:

#### Features:
- **Large Text Area**: 600x500 pixel resizable dialog
- **Multi-line Input**: Full text editing with word wrap and scrollbars
- **Real-time Statistics**: Character and word count display
- **Input Validation**: Prevents empty prompts with helpful feedback
- **Auto-name Generation**: Automatically suggests names based on content
- **Save Functionality**: Save prompts directly while creating them
- **Quick Access**: Buttons for recent and favorite prompts

#### Usage:
1. Start a recursive or iterative analysis
2. The enhanced dialog appears automatically
3. Enter your analysis prompt in the large text area
4. Optionally save the prompt by entering a name and clicking "Save & Use"
5. Use quick access buttons to load previously saved prompts

### Prompt Manager Interface

Access the full Prompt Manager through **Tools** → **Prompt Manager** in the GUI.

#### Main Features:

**Prompt List (Left Panel):**
- Tree view showing all saved prompts
- Columns: Name, Category, Created date, Usage count
- Favorite prompts marked with ★

**Preview Panel (Right Panel):**
- Full text preview of selected prompts
- Metadata and usage statistics

**Toolbar:**
- Search box to filter prompts by name or content
- Category filter dropdown
- Refresh button

#### Managing Prompts:

**Creating New Prompts:**
1. Click "New Prompt" button
2. Enter prompt in the enhanced dialog
3. Save with a descriptive name

**Editing Prompts:**
1. Select prompt from list
2. Double-click or use context menu
3. Make changes and save

**Organizing Prompts:**
- Use categories to group related prompts
- Mark frequently used prompts as favorites (★)
- Use descriptive names for easy searching

### Quick Access Features

**Recent Prompts:**
- Automatically tracks recently used prompts
- Shows as buttons in the enhanced dialog
- Maintains usage statistics

**Favorite Prompts:**
- Mark important prompts with ★ for quick access
- Appear prominently in quick access area
- Easy to toggle on/off

### Import/Export

**Exporting Prompts:**
1. Open Prompt Manager
2. Click "Export..." button
3. Choose location and save as JSON file
4. Includes all metadata and usage statistics

**Importing Prompts:**
1. Click "Import..." button
2. Select JSON file from another instance
3. Prompts are added to your collection
4. Automatic duplicate handling

### Usage Analytics

View detailed statistics about your prompt usage:

1. Open Prompt Manager
2. Click "Analytics" button
3. View comprehensive data:
   - Total prompts and usage counts
   - Most frequently used prompts
   - Category breakdown
   - Recent activity (last 7 days)

### Best Practices

**Organization:**
- Use clear, descriptive names
- Categorize prompts consistently
- Regular cleanup of unused prompts
- Export backups regularly

**Creating Effective Prompts:**
- Be specific and detailed
- Create template prompts for common patterns
- Test and refine based on results
- Use analytics to identify most effective prompts

## Advanced Features

### Model Selection

Choose different models for different needs:
- **llama2**: Good balance of speed and quality
- **llama2:13b**: Higher quality, slower
- **mistral**: Fast, good for simple analysis
- **codellama**: Best for technical/programming topics

### Prompt Chaining

For complex analysis, use prompt chaining:
1. Create base analysis prompt
2. Add refinement prompts
3. Chain prompts for multi-step analysis

### Performance Optimization

**For Large Hierarchies**:
- Use batch processing mode
- Reduce concurrent requests in config
- Use smaller models for initial passes

**For Better Quality**:
- Use larger models (13b, 70b)
- Increase temperature for creativity
- Use iterative analysis with multiple passes

### Configuration Customization

Edit `config/settings.yaml`:
```yaml
analysis:
  default_type: "recursive"
  max_depth: 5
  batch_size: 10
  quality_threshold: 0.7

ollama:
  default_model: "llama2"
  timeout: 60
  temperature: 0.7
```

## Troubleshooting

### Common Issues

#### "Ollama connection failed"
1. Check if Ollama is running: `ollama serve`
2. Verify URL in config: `http://localhost:11434`
3. Test connection: `curl http://localhost:11434/api/tags`

#### "Model not found"
1. List available models: `ollama list`
2. Pull required model: `ollama pull llama2`
3. Update config with available model name

#### "Analysis stuck or slow"
1. Check model size and system resources
2. Reduce batch size in configuration
3. Use smaller model for testing
4. Check network connectivity to Ollama

#### "GUI won't start"
1. Install GUI dependencies: `pip install matplotlib networkx`
2. On Linux: `sudo apt-get install python3-tk`
3. Check Python tkinter: `python -c "import tkinter"`

#### "Import/Export errors"
1. Check file permissions and paths
2. Validate CSV format (proper headers, encoding)
3. Ensure JSON is valid format
4. Check available disk space

### Getting Help

1. **System Check**: `python -m src.main --check-system`
2. **Debug Mode**: `python -m src.main --debug`
3. **Log Files**: Check `logs/` directory for detailed errors
4. **Test Suite**: `python run_tests.py` to verify installation

### Performance Tips

**Memory Usage**:
- Close other applications during large analyses
- Use smaller batch sizes for limited RAM
- Monitor system resources during analysis

**Speed Optimization**:
- Use local Ollama installation
- Choose appropriate model size for your hardware
- Enable caching in configuration
- Use SSD storage for better I/O performance

**Quality Improvement**:
- Write specific, detailed system prompts
- Use hierarchical context effectively
- Review and refine hierarchy structure
- Use iterative analysis for important topics

---

For more detailed technical information, see the [API Documentation](API_REFERENCE.md) and [Developer Guide](DEVELOPER_GUIDE.md).
